#!/usr/bin/env python3
"""Test script to verify training pipeline works correctly."""

import torch
import numpy as np
import yaml
import os
from torch.utils.data import Dataset, DataLoader

from rlepose.models.builder import build_sppe, build_loss
from rlepose.pose_tracking_trainer import train_pose_tracking


class DummyDataset(Dataset):
    """Dummy dataset for testing."""
    
    def __init__(self, num_samples=10, image_size=(256, 192), heatmap_size=(64, 48), num_joints=17):
        self.num_samples = num_samples
        self.image_size = image_size
        self.heatmap_size = heatmap_size
        self.num_joints = num_joints
    
    def __len__(self):
        return self.num_samples
    
    def __getitem__(self, idx):
        # Create dummy data
        images = torch.randn(3, self.image_size[0], self.image_size[1])
        target_hm = torch.randn(self.num_joints, self.heatmap_size[0], self.heatmap_size[1])
        tracking_targets = [idx % 3]  # Simple track IDs
        
        return {
            'images': images,
            'target_hm': target_hm,
            'tracking_targets': tracking_targets,
            'vid_id': f'dummy_{idx}'
        }


def test_training():
    """Test the training pipeline."""
    
    print("Testing training pipeline...")
    
    # Load configuration
    with open('configs/posetrack21_bottom_up.yaml', 'r') as f:
        cfg = yaml.safe_load(f)
    
    # Modify config for quick testing
    cfg['TRAIN']['END_EPOCH'] = 2
    cfg['TRAIN']['BATCH_SIZE'] = 2
    cfg['LOG_INTERVAL'] = 2
    cfg['VIS_INTERVAL'] = 5
    
    # Create dummy datasets
    train_dataset = DummyDataset(num_samples=8)
    val_dataset = DummyDataset(num_samples=4)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=cfg['TRAIN']['BATCH_SIZE'], shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=cfg['TRAIN']['BATCH_SIZE'], shuffle=False)
    
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    
    # Build model and loss
    model = build_sppe(cfg['MODEL'], cfg['DATA_PRESET'])
    criterion = build_loss(cfg['LOSS'])
    
    # Create optimizer
    optimizer = torch.optim.Adam(model.parameters(), lr=cfg['TRAIN']['LR'])
    scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=1, gamma=0.9)
    
    # Create output directory
    output_dir = './test_output'
    os.makedirs(output_dir, exist_ok=True)
    
    print("Starting training test...")
    
    try:
        # Run training for a few steps
        train_pose_tracking(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            criterion=criterion,
            optimizer=optimizer,
            scheduler=scheduler,
            cfg=cfg,
            output_dir=output_dir
        )
        
        print("Training test completed successfully! ✅")
        
        # Check if files were created
        expected_files = ['best_model.pth', 'checkpoint_epoch_001.pth']
        for file in expected_files:
            file_path = os.path.join(output_dir, file)
            if os.path.exists(file_path):
                print(f"✅ {file} created successfully")
            else:
                print(f"❌ {file} not found")
        
        # Check visualization directory
        vis_dir = os.path.join(output_dir, 'visualizations')
        if os.path.exists(vis_dir):
            vis_files = os.listdir(vis_dir)
            print(f"✅ Visualization directory created with {len(vis_files)} files")
        else:
            print("❌ Visualization directory not found")
            
    except Exception as e:
        print(f"Training test failed: {e}")
        raise


if __name__ == '__main__':
    test_training()
