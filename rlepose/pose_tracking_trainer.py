"""Trainer for pose estimation and tracking."""
import os
import time
import torch
import torch.nn as nn
from torch.utils.data import DataLoader

from .utils.metrics import DataLogger, compute_mota, compute_ap_tracking, heatmap_to_keypoints
from .utils.visualization import save_training_visualization


class PoseTrackingEvaluator:
    """Evaluator for pose estimation and tracking."""
    
    def __init__(self, num_joints=17, oks_thresholds=None):
        self.num_joints = num_joints
        self.oks_thresholds = oks_thresholds or [0.5, 0.55, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95]
        self.reset()
    
    def reset(self):
        """Reset evaluation metrics."""
        self.pred_tracks = []
        self.gt_tracks = []
        self.oks_scores = []
    
    def add_batch(self, pred_heatmaps, gt_heatmaps, tracking_targets=None):
        """Add a batch of predictions and ground truth."""
        # Convert heatmaps to keypoints
        pred_keypoints = heatmap_to_keypoints(pred_heatmaps.cpu().numpy())
        gt_keypoints = heatmap_to_keypoints(gt_heatmaps.cpu().numpy())
        
        batch_size = pred_keypoints.shape[0]
        
        for b in range(batch_size):
            pred_kpts = pred_keypoints[b]
            gt_kpts = gt_keypoints[b]
            
            # Compute OKS for this sample
            for j in range(self.num_joints):
                if gt_kpts[j, 2] > 0:  # Visible keypoint
                    # Simple distance-based similarity
                    dx = pred_kpts[j, 0] - gt_kpts[j, 0]
                    dy = pred_kpts[j, 1] - gt_kpts[j, 1]
                    dist = (dx**2 + dy**2)**0.5
                    oks = max(0, 1 - dist / 50.0)  # Simplified OKS
                    self.oks_scores.append(oks)
            
            # Store tracks for MOTA computation
            if tracking_targets is not None:
                pred_track = {
                    'keypoints': pred_kpts,
                    'track_id': 0  # Simplified for now
                }

                # Handle different tracking_targets formats
                if isinstance(tracking_targets, list) and len(tracking_targets) > b:
                    track_id = tracking_targets[b]
                    if isinstance(track_id, list) and len(track_id) > 0:
                        track_id = track_id[0]
                    elif not isinstance(track_id, int):
                        track_id = 0
                else:
                    track_id = 0

                gt_track = {
                    'keypoints': gt_kpts,
                    'track_id': track_id,
                    'area': 1.0
                }
                
                # Add to frame-level tracks
                if len(self.pred_tracks) <= b:
                    self.pred_tracks.extend([[] for _ in range(b + 1 - len(self.pred_tracks))])
                    self.gt_tracks.extend([[] for _ in range(b + 1 - len(self.gt_tracks))])
                
                self.pred_tracks[b].append(pred_track)
                self.gt_tracks[b].append(gt_track)
    
    def compute_metrics(self):
        """Compute final evaluation metrics."""
        metrics = {}
        
        # Compute AP for different OKS thresholds
        ap_scores = []
        for threshold in self.oks_thresholds:
            ap = compute_ap_tracking(self.oks_scores, threshold)
            ap_scores.append(ap)
            metrics[f'AP_{threshold:.2f}'] = ap
        
        metrics['AP'] = sum(ap_scores) / len(ap_scores) if ap_scores else 0.0
        
        # Compute MOTA
        if len(self.pred_tracks) > 0 and len(self.gt_tracks) > 0:
            mota_result = compute_mota(self.pred_tracks, self.gt_tracks)
            metrics.update(mota_result)
        else:
            metrics['mota'] = 0.0
        
        return metrics


def train_pose_tracking(model, train_loader, val_loader, criterion, optimizer, 
                       scheduler, cfg, output_dir='./output'):
    """Train pose estimation and tracking model."""
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    vis_dir = os.path.join(output_dir, 'visualizations')
    os.makedirs(vis_dir, exist_ok=True)
    
    # Training parameters
    num_epochs = cfg['TRAIN']['END_EPOCH']
    log_interval = cfg.get('LOG_INTERVAL', 100)
    vis_interval = cfg.get('VIS_INTERVAL', 500)
    
    # Loggers
    loss_logger = DataLogger()
    
    best_ap = 0.0
    
    for epoch in range(num_epochs):
        print(f"\nEpoch {epoch + 1}/{num_epochs}")
        print("-" * 50)
        
        # Training phase
        model.train()
        loss_logger.clear()
        
        for step, batch in enumerate(train_loader):
            # Move data to device
            images = batch['images'].to(device)
            target_hm = batch['target_hm'].to(device)
            tracking_targets = batch.get('tracking_targets', None)
            
            # Handle sequence dimension
            if images.dim() == 5:  # (B, T, C, H, W)
                batch_size, seq_len = images.shape[:2]
                images = images.view(-1, *images.shape[2:])
                target_hm = target_hm.view(-1, *target_hm.shape[2:])
            
            # Forward pass
            optimizer.zero_grad()
            output = model(images)
            
            # Prepare labels for loss computation
            labels = {
                'target_hm': target_hm,
                'tracking_targets': tracking_targets
            }
            
            # Compute loss
            loss = criterion(output, labels)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            # Update logger
            loss_logger.update(loss.item(), images.size(0))
            
            # Log progress
            if step % log_interval == 0:
                print(f"Step {step:6d} | Loss: {loss_logger.avg:.6f}")
            
            # Save visualization
            if step % vis_interval == 0:
                with torch.no_grad():
                    save_training_visualization(
                        images[:4], output['heatmap'][:4], target_hm[:4], 
                        tracking_targets, vis_dir, epoch, step
                    )
        
        # Validation phase
        print(f"\nValidation after epoch {epoch + 1}")
        val_metrics = validate_pose_tracking(model, val_loader, device)
        
        # Print metrics
        print(f"Validation AP: {val_metrics['AP']:.4f}")
        print(f"Validation MOTA: {val_metrics['mota']:.4f}")
        
        # Save best model
        if val_metrics['AP'] > best_ap:
            best_ap = val_metrics['AP']
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_ap': best_ap,
                'metrics': val_metrics
            }, os.path.join(output_dir, 'best_model.pth'))
            print(f"New best model saved with AP: {best_ap:.4f}")
        
        # Save checkpoint
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'metrics': val_metrics
        }, os.path.join(output_dir, f'checkpoint_epoch_{epoch:03d}.pth'))
        
        # Update learning rate
        if scheduler:
            scheduler.step()
        
        print(f"Epoch {epoch + 1} completed. Average loss: {loss_logger.avg:.6f}")


def validate_pose_tracking(model, val_loader, device):
    """Validate pose estimation and tracking model."""
    model.eval()
    evaluator = PoseTrackingEvaluator(num_joints=17)
    
    with torch.no_grad():
        for batch in val_loader:
            images = batch['images'].to(device)
            target_hm = batch['target_hm'].to(device)
            tracking_targets = batch.get('tracking_targets', None)
            
            # Handle sequence dimension
            if images.dim() == 5:  # (B, T, C, H, W)
                images = images.view(-1, *images.shape[2:])
                target_hm = target_hm.view(-1, *target_hm.shape[2:])
            
            # Forward pass
            output = model(images)
            pred_hm = output['heatmap']
            
            # Add to evaluator
            evaluator.add_batch(pred_hm, target_hm, tracking_targets)
    
    # Compute metrics
    metrics = evaluator.compute_metrics()
    return metrics
