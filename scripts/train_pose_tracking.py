#!/usr/bin/env python3
"""Training script for pose estimation and tracking."""

import argparse
import os
import sys
import yaml
import torch
import torch.optim as optim
from torch.utils.data import DataLoader

# Add parent directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from rlepose.models.builder import build_sppe, build_loss, build_dataset
from rlepose.pose_tracking_trainer import train_pose_tracking
from rlepose.utils.config import update_config


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train pose estimation and tracking model')
    parser.add_argument('--cfg', 
                       help='experiment configure file name',
                       default='configs/posetrack21_bottom_up.yaml',
                       type=str)
    parser.add_argument('--output-dir',
                       help='output directory',
                       default='./output',
                       type=str)
    parser.add_argument('--resume',
                       help='resume from checkpoint',
                       default='',
                       type=str)
    parser.add_argument('--gpu',
                       help='gpu id for multiprocessing training',
                       default='0',
                       type=str)
    
    args = parser.parse_args()
    return args


def load_config(config_file):
    """Load configuration from YAML file."""
    with open(config_file, 'r') as f:
        config = yaml.safe_load(f)
    return config


def create_data_loaders(cfg):
    """Create training and validation data loaders."""
    
    # Build datasets
    train_dataset = build_dataset(cfg['DATASET']['TRAIN'], cfg['DATA_PRESET'])
    val_dataset = build_dataset(cfg['DATASET']['VAL'], cfg['DATA_PRESET'])
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=cfg['TRAIN']['BATCH_SIZE'],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=cfg['TRAIN']['BATCH_SIZE'],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
        drop_last=False
    )
    
    return train_loader, val_loader


def create_model_and_optimizer(cfg):
    """Create model, loss function, and optimizer."""
    
    # Build model
    model = build_sppe(cfg['MODEL'], cfg['DATA_PRESET'])
    
    # Build loss function
    criterion = build_loss(cfg['LOSS'])
    
    # Create optimizer
    if cfg['TRAIN']['OPTIMIZER'] == 'adam':
        optimizer = optim.Adam(
            model.parameters(),
            lr=cfg['TRAIN']['LR'],
            weight_decay=1e-4
        )
    elif cfg['TRAIN']['OPTIMIZER'] == 'sgd':
        optimizer = optim.SGD(
            model.parameters(),
            lr=cfg['TRAIN']['LR'],
            momentum=0.9,
            weight_decay=1e-4
        )
    else:
        raise ValueError(f"Unsupported optimizer: {cfg['TRAIN']['OPTIMIZER']}")
    
    # Create learning rate scheduler
    scheduler = optim.lr_scheduler.MultiStepLR(
        optimizer,
        milestones=cfg['TRAIN']['LR_STEP'],
        gamma=cfg['TRAIN']['LR_FACTOR']
    )
    
    return model, criterion, optimizer, scheduler


def main():
    """Main training function."""
    args = parse_args()
    
    # Set GPU
    os.environ['CUDA_VISIBLE_DEVICES'] = args.gpu
    
    # Load configuration
    cfg = load_config(args.cfg)
    
    # Update config with command line arguments
    cfg['OUTPUT_DIR'] = args.output_dir
    
    print("Configuration:")
    print(yaml.dump(cfg, default_flow_style=False))
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Save configuration
    with open(os.path.join(args.output_dir, 'config.yaml'), 'w') as f:
        yaml.dump(cfg, f, default_flow_style=False)
    
    # Create data loaders
    print("Creating data loaders...")
    train_loader, val_loader = create_data_loaders(cfg)
    print(f"Training samples: {len(train_loader.dataset)}")
    print(f"Validation samples: {len(val_loader.dataset)}")
    
    # Create model and optimizer
    print("Creating model and optimizer...")
    model, criterion, optimizer, scheduler = create_model_and_optimizer(cfg)
    
    # Print model info
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    # Resume from checkpoint if specified
    start_epoch = 0
    if args.resume and os.path.isfile(args.resume):
        print(f"Loading checkpoint from {args.resume}")
        checkpoint = torch.load(args.resume, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        start_epoch = checkpoint['epoch'] + 1
        print(f"Resumed from epoch {start_epoch}")
    
    # Start training
    print("Starting training...")
    train_pose_tracking(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        criterion=criterion,
        optimizer=optimizer,
        scheduler=scheduler,
        cfg=cfg,
        output_dir=args.output_dir
    )
    
    print("Training completed!")


if __name__ == '__main__':
    main()
