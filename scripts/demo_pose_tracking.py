#!/usr/bin/env python3
"""Demo script for pose estimation and tracking."""

import argparse
import os
import sys
import yaml
import cv2
import torch
import numpy as np
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from rlepose.models.builder import build_sppe
from rlepose.utils.visualization import draw_multiple_skeletons, heatmap_to_keypoints


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Demo for pose estimation and tracking')
    parser.add_argument('--cfg', 
                       help='experiment configure file name',
                       default='configs/posetrack21_bottom_up.yaml',
                       type=str)
    parser.add_argument('--checkpoint',
                       help='checkpoint file',
                       required=True,
                       type=str)
    parser.add_argument('--input',
                       help='input video file or image directory',
                       required=True,
                       type=str)
    parser.add_argument('--output',
                       help='output directory',
                       default='./demo_output',
                       type=str)
    parser.add_argument('--device',
                       help='device to use (cuda or cpu)',
                       default='cuda',
                       type=str)
    parser.add_argument('--confidence-threshold',
                       help='confidence threshold for keypoint visualization',
                       default=0.1,
                       type=float)
    parser.add_argument('--save-video',
                       help='save output as video',
                       action='store_true')
    
    args = parser.parse_args()
    return args


def load_config(config_file):
    """Load configuration from YAML file."""
    with open(config_file, 'r') as f:
        config = yaml.safe_load(f)
    return config


def load_model(cfg, checkpoint_path, device):
    """Load trained model from checkpoint."""
    # Build model
    model = build_sppe(cfg['MODEL'], cfg['DATA_PRESET'])
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # Move to device and set to eval mode
    model = model.to(device)
    model.eval()
    
    return model


def preprocess_image(image, input_size):
    """Preprocess image for model input."""
    # Resize image
    h, w = image.shape[:2]
    target_h, target_w = input_size
    
    # Calculate scale to maintain aspect ratio
    scale = min(target_w / w, target_h / h)
    new_w = int(w * scale)
    new_h = int(h * scale)
    
    # Resize image
    resized = cv2.resize(image, (new_w, new_h))
    
    # Create padded image
    padded = np.zeros((target_h, target_w, 3), dtype=np.uint8)
    start_x = (target_w - new_w) // 2
    start_y = (target_h - new_h) // 2
    padded[start_y:start_y + new_h, start_x:start_x + new_w] = resized
    
    # Convert to tensor and normalize
    tensor = torch.from_numpy(padded).permute(2, 0, 1).float() / 255.0
    
    return tensor, scale, (start_x, start_y)


def postprocess_keypoints(keypoints, scale, offset, original_size):
    """Postprocess keypoints to original image coordinates."""
    start_x, start_y = offset
    orig_h, orig_w = original_size
    
    # Adjust for padding offset
    keypoints[:, 0] -= start_x
    keypoints[:, 1] -= start_y
    
    # Scale back to original size
    keypoints[:, :2] /= scale
    
    # Clip to image boundaries
    keypoints[:, 0] = np.clip(keypoints[:, 0], 0, orig_w - 1)
    keypoints[:, 1] = np.clip(keypoints[:, 1], 0, orig_h - 1)
    
    return keypoints


def process_video(video_path, model, cfg, output_dir, device, confidence_threshold, save_video):
    """Process video file."""
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        raise ValueError(f"Cannot open video: {video_path}")
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"Video info: {width}x{height}, {fps} FPS, {total_frames} frames")
    
    # Setup video writer if saving video
    if save_video:
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out_video_path = os.path.join(output_dir, 'output_video.mp4')
        video_writer = cv2.VideoWriter(out_video_path, fourcc, fps, (width, height))
    
    input_size = cfg['DATA_PRESET']['IMAGE_SIZE']
    frame_idx = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Preprocess frame
        input_tensor, scale, offset = preprocess_image(frame, input_size)
        input_batch = input_tensor.unsqueeze(0).to(device)
        
        # Run inference
        with torch.no_grad():
            output = model(input_batch)
            pred_heatmaps = output['heatmap'].cpu().numpy()
        
        # Convert heatmaps to keypoints
        keypoints_batch = heatmap_to_keypoints(pred_heatmaps, threshold=confidence_threshold)
        
        # Process each person detected
        result_frame = frame.copy()
        for person_idx, keypoints in enumerate(keypoints_batch):
            # Postprocess keypoints
            keypoints = postprocess_keypoints(
                keypoints.copy(), scale, offset, (height, width)
            )
            
            # Draw skeleton
            result_frame = draw_multiple_skeletons(
                result_frame, [keypoints], [person_idx], confidence_threshold
            )
        
        # Save frame
        frame_output_path = os.path.join(output_dir, f'frame_{frame_idx:06d}.jpg')
        cv2.imwrite(frame_output_path, result_frame)
        
        # Add to video if saving
        if save_video:
            video_writer.write(result_frame)
        
        frame_idx += 1
        if frame_idx % 30 == 0:
            print(f"Processed {frame_idx}/{total_frames} frames")
    
    cap.release()
    if save_video:
        video_writer.release()
        print(f"Video saved to: {out_video_path}")
    
    print(f"Processed {frame_idx} frames")


def process_images(image_dir, model, cfg, output_dir, device, confidence_threshold):
    """Process directory of images."""
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    image_paths = []
    
    for ext in image_extensions:
        image_paths.extend(Path(image_dir).glob(f'*{ext}'))
        image_paths.extend(Path(image_dir).glob(f'*{ext.upper()}'))
    
    image_paths = sorted(image_paths)
    
    if not image_paths:
        raise ValueError(f"No images found in directory: {image_dir}")
    
    print(f"Found {len(image_paths)} images")
    
    input_size = cfg['DATA_PRESET']['IMAGE_SIZE']
    
    for idx, image_path in enumerate(image_paths):
        # Load image
        image = cv2.imread(str(image_path))
        if image is None:
            print(f"Warning: Cannot load image {image_path}")
            continue
        
        height, width = image.shape[:2]
        
        # Preprocess image
        input_tensor, scale, offset = preprocess_image(image, input_size)
        input_batch = input_tensor.unsqueeze(0).to(device)
        
        # Run inference
        with torch.no_grad():
            output = model(input_batch)
            pred_heatmaps = output['heatmap'].cpu().numpy()
        
        # Convert heatmaps to keypoints
        keypoints_batch = heatmap_to_keypoints(pred_heatmaps, threshold=confidence_threshold)
        
        # Process each person detected
        result_image = image.copy()
        for person_idx, keypoints in enumerate(keypoints_batch):
            # Postprocess keypoints
            keypoints = postprocess_keypoints(
                keypoints.copy(), scale, offset, (height, width)
            )
            
            # Draw skeleton
            result_image = draw_multiple_skeletons(
                result_image, [keypoints], [person_idx], confidence_threshold
            )
        
        # Save result
        output_path = os.path.join(output_dir, f'result_{image_path.stem}.jpg')
        cv2.imwrite(output_path, result_image)
        
        if (idx + 1) % 10 == 0:
            print(f"Processed {idx + 1}/{len(image_paths)} images")
    
    print(f"Processed {len(image_paths)} images")


def main():
    """Main demo function."""
    args = parse_args()
    
    # Load configuration
    cfg = load_config(args.cfg)
    
    # Create output directory
    os.makedirs(args.output, exist_ok=True)
    
    # Setup device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load model
    print("Loading model...")
    model = load_model(cfg, args.checkpoint, device)
    print("Model loaded successfully")
    
    # Process input
    if os.path.isfile(args.input):
        # Process video file
        print(f"Processing video: {args.input}")
        process_video(
            args.input, model, cfg, args.output, device, 
            args.confidence_threshold, args.save_video
        )
    elif os.path.isdir(args.input):
        # Process image directory
        print(f"Processing images in directory: {args.input}")
        process_images(
            args.input, model, cfg, args.output, device, 
            args.confidence_threshold
        )
    else:
        raise ValueError(f"Input path does not exist: {args.input}")
    
    print(f"Results saved to: {args.output}")


if __name__ == '__main__':
    main()
