#!/usr/bin/env python3
"""Test script to verify the model works correctly."""

import torch
import numpy as np
import yaml
from rlepose.models.builder import build_sppe, build_loss


def test_model():
    """Test the BottomUpPoseTracker model."""
    
    # Load configuration
    with open('configs/posetrack21_bottom_up.yaml', 'r') as f:
        cfg = yaml.safe_load(f)
    
    print("Testing BottomUpPoseTracker model...")
    
    # Build model
    model = build_sppe(cfg['MODEL'], cfg['DATA_PRESET'])
    print(f"Model created successfully")
    
    # Print model info
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    # Test forward pass
    batch_size = 2
    channels = 3
    height, width = cfg['DATA_PRESET']['IMAGE_SIZE']
    
    # Create dummy input
    dummy_input = torch.randn(batch_size, channels, height, width)
    print(f"Input shape: {dummy_input.shape}")
    
    # Forward pass
    model.eval()
    with torch.no_grad():
        output = model(dummy_input)
    
    print("Forward pass successful!")
    print(f"Output keys: {list(output.keys())}")
    
    if 'heatmap' in output:
        hm_shape = output['heatmap'].shape
        print(f"Heatmap shape: {hm_shape}")
        expected_shape = (batch_size, cfg['DATA_PRESET']['NUM_JOINTS'], 
                         cfg['DATA_PRESET']['HEATMAP_SIZE'][1], 
                         cfg['DATA_PRESET']['HEATMAP_SIZE'][0])
        print(f"Expected heatmap shape: {expected_shape}")
        assert hm_shape == expected_shape, f"Heatmap shape mismatch: {hm_shape} vs {expected_shape}"
    
    if 'tracking_embedding' in output:
        emb_shape = output['tracking_embedding'].shape
        print(f"Tracking embedding shape: {emb_shape}")
    
    # Test loss function
    print("\nTesting loss function...")
    criterion = build_loss(cfg['LOSS'])
    
    # Create dummy labels
    num_joints = cfg['DATA_PRESET']['NUM_JOINTS']
    hm_h, hm_w = cfg['DATA_PRESET']['HEATMAP_SIZE']

    labels = {
        'target_hm': torch.randn(batch_size, num_joints, hm_w, hm_h),  # Note: swapped order to match output
        'tracking_targets': [0, 1]  # dummy track IDs
    }
    
    # Compute loss
    loss = criterion(output, labels)
    print(f"Loss computed successfully: {loss.item():.6f}")
    
    print("\nAll tests passed! ✅")


if __name__ == '__main__':
    test_model()
