# Bottom-Up Pose Estimation and Tracking

이 프로젝트는 PoseTrack21 데이터셋을 사용하여 bottom-up 방식의 pose estimation과 tracking을 동시에 수행하는 multi-task 모델을 구현합니다.

## 주요 특징

- **Bottom-up 방식**: keypoint 단위 히트맵 추정
- **Multi-task 학습**: pose estimation과 tracking을 동시에 수행
- **ResNet backbone**: ResNet-50 기반 feature extraction
- **실시간 시각화**: 학습 중 중간 결과 시각화
- **성능 메트릭**: AP (Average Precision)와 MOTA (Multiple Object Tracking Accuracy) 계산
- **Demo 지원**: 비디오 및 이미지 시퀀스 처리

## 모델 아키텍처

### BottomUpPoseTracker
- **Backbone**: ResNet-50 (ImageNet pretrained)
- **Deconvolution Head**: 히트맵 생성을 위한 upsampling
- **Heatmap Layer**: 17개 keypoint에 대한 히트맵 출력
- **Tracking Head**: temporal association을 위한 embedding

### Loss Function
- **Pose Loss**: MSE loss for heatmap prediction
- **Tracking Loss**: Contrastive loss for temporal consistency
- **Multi-task Loss**: 가중합으로 결합

## 데이터셋

### PoseTrack21
- **Keypoints**: 17개 keypoint (COCO format 호환)
- **Sequence Length**: 연속 프레임 처리 (기본 2프레임)
- **Annotations**: person_id와 track_id 포함

## 설치 및 설정

### 1. 환경 설정
```bash
# 필요한 패키지 설치
pip install torch torchvision opencv-python matplotlib scipy easydict pyyaml
```

### 2. 데이터셋 준비
PoseTrack21 데이터셋이 `datasets/PoseTrack21` 디렉토리에 있어야 합니다.

```
datasets/PoseTrack21/
├── data/
│   ├── images/
│   └── posetrack_data/
│       ├── train/
│       └── val/
```

## 사용법

### 1. 학습

```bash
# 기본 설정으로 학습
python scripts/train_pose_tracking.py --cfg configs/posetrack21_bottom_up.yaml

# 출력 디렉토리 지정
python scripts/train_pose_tracking.py \
    --cfg configs/posetrack21_bottom_up.yaml \
    --output-dir ./my_output

# 체크포인트에서 재시작
python scripts/train_pose_tracking.py \
    --cfg configs/posetrack21_bottom_up.yaml \
    --resume ./output/checkpoint_epoch_050.pth
```

### 2. Demo 실행

```bash
# 비디오 파일 처리
python scripts/demo_pose_tracking.py \
    --cfg configs/posetrack21_bottom_up.yaml \
    --checkpoint ./output/best_model.pth \
    --input ./test_video.mp4 \
    --output ./demo_output \
    --save-video

# 이미지 디렉토리 처리
python scripts/demo_pose_tracking.py \
    --cfg configs/posetrack21_bottom_up.yaml \
    --checkpoint ./output/best_model.pth \
    --input ./test_images/ \
    --output ./demo_output
```

## 설정 파일

### configs/posetrack21_bottom_up.yaml
주요 설정 옵션:

```yaml
# 데이터셋 설정
DATASET:
  TRAIN:
    TYPE: 'PoseTrack21'
    ROOT: './datasets/PoseTrack21'
    SEQUENCE_LENGTH: 2

# 모델 설정
MODEL:
  TYPE: 'BottomUpPoseTracker'
  NUM_LAYERS: 50

# 학습 설정
TRAIN:
  BATCH_SIZE: 8
  END_EPOCH: 100
  LR: 0.001

# Loss 설정
LOSS:
  TYPE: 'PoseTrackingLoss'
  POSE_WEIGHT: 1.0
  TRACKING_WEIGHT: 0.1
```

## 출력 결과

### 학습 중 출력
- **체크포인트**: `output/checkpoint_epoch_XXX.pth`
- **최고 성능 모델**: `output/best_model.pth`
- **시각화 이미지**: `output/visualizations/`
- **설정 파일**: `output/config.yaml`

### 시각화 이미지
- 예측 스켈레톤과 GT 스켈레톤 비교
- 다양한 색상으로 track ID 구분
- 일정 스텝마다 자동 저장

### 성능 메트릭
- **AP (Average Precision)**: pose estimation 성능
- **MOTA (Multiple Object Tracking Accuracy)**: tracking 성능
- **매 에포크마다 출력**: 중간 결과 모니터링

## 파일 구조

```
rlepose/
├── models/
│   ├── bottom_up_pose_tracker.py    # 메인 모델
│   └── criterion.py                 # Loss functions
├── datasets/
│   └── posetrack21.py              # PoseTrack21 데이터 로더
├── utils/
│   ├── metrics.py                  # 평가 메트릭
│   └── visualization.py           # 시각화 유틸리티
└── pose_tracking_trainer.py       # 학습 파이프라인

scripts/
├── train_pose_tracking.py          # 학습 스크립트
└── demo_pose_tracking.py          # Demo 스크립트

configs/
└── posetrack21_bottom_up.yaml     # 설정 파일
```

## 주요 기능

### 1. Multi-task Learning
- Pose estimation과 tracking을 동시에 학습
- 가중치 조절을 통한 task balance

### 2. 실시간 모니터링
- 학습 중 loss 및 메트릭 출력
- 시각화 이미지 자동 저장

### 3. Resume 기능
- 체크포인트에서 학습 재시작
- 최고 성능 모델 자동 저장

### 4. 유연한 Demo
- 비디오 파일 및 이미지 시퀀스 지원
- 결과 비디오 저장 옵션

## 성능 최적화 팁

1. **배치 크기 조절**: GPU 메모리에 맞게 BATCH_SIZE 조정
2. **학습률 스케줄링**: LR_STEP과 LR_FACTOR 조정
3. **Loss 가중치**: POSE_WEIGHT와 TRACKING_WEIGHT 균형 조정
4. **시퀀스 길이**: SEQUENCE_LENGTH로 temporal context 조절

## 문제 해결

### 1. 메모리 부족
- BATCH_SIZE 줄이기
- 이미지 크기 줄이기 (IMAGE_SIZE, HEATMAP_SIZE)

### 2. 학습 불안정
- 학습률 줄이기
- Gradient clipping 활용

### 3. 성능 저하
- 더 많은 에포크 학습
- 데이터 augmentation 강화
